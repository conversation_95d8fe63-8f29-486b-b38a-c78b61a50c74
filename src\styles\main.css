.center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dq_content>input {
    outline: none;
    border: none;
    height: 100%;
    width: 100%;
    font-family: Source <PERSON>, Source <PERSON> Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    text-indent: 10px;
}

.dq_content>input::placeholder {
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: 400;
    font-size: 24px;
    color: #78808A;
}

.dq_content>input:disabled {
    background: rgba(118, 118, 118, 0.2)
}

.dq_content .selectDiv {
    background: #dbe6f1;
    border-radius: 0px 0px 0px 0px;
    height: 100%;
    width: 100%;
    font-family: Source <PERSON>, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #3173c6;
    overflow: hidden;
}

body {
    font-family: Source <PERSON>, Source <PERSON> Sans CN;
}