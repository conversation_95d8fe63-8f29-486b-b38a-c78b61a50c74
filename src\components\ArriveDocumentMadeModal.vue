<template>
    <a-modal :open="open" :footer="null" :closable="false" width="1087px" :centered="true"
        wrapClassName="arrive-documnet-dialog" :maskClosable="false">
        <template #title>
            <div class="modal_title" style="display: flex; align-items: center;">送达地址确认书<img src="@/assets/wenhao.png" @click="tishiOpen = true" style="margin-left: 10px;width: 48px;height: 48px;"/></div>
        </template>
        <div class="modal_content">
            <div class="delivery-table">
                <div class="delivery-row header">
                    <div class="cell type">固定送达</div>
                    <div class="cell content">人民法院在线服务</div>
                    <div class="cell action"></div>
                </div>
                <!-- Mail -->
                <div class="delivery-row">
                    <div class="cell type required">
                        <div class="title-line">
                            <span>邮寄送达地址</span>
                        </div>
                        <span class="item-count">(共{{ mailAddresses.length }}条)</span>
                    </div>
                    <div class="cell content">
                        <template v-if="mailAddresses.length > 0">
                            <div class="scrollable-list" ref="mailList" @scroll="handleScroll('mail')">
                                <div v-for="item in mailAddresses" :key="item.bh" class="list-item">
                                    <input type="radio" class="custom-radio" :value="item.bh" :checked="selectedMailId === item.bh"
                                        @click="handleSelection('mail', item.bh)" name="mail-address" />
                                    <span>{{ item.text }}</span>
                                </div>
                            </div>
                            <img :src="expandIcon" class="expand-icon" @click="expandList('mail')" />
                        </template>
                        <div v-else class="placeholder">必须添加邮寄送达地址</div>
                    </div>
                    <div class="cell action">
                        <button class="add-btn" @click="mailArriveAddressModalOpen = true">+添加</button>
                    </div>
                </div>
                <!-- SMS -->
                <div class="delivery-row">
                    <div class="cell type">
                        <div class="title-line">
                            <span>短信送达</span>
                        </div>
                        <span class="item-count">(共{{ smsNumbers.length }}条)</span>
                    </div>
                    <div class="cell content">
                        <template v-if="smsNumbers.length > 0">
                            <div class="scrollable-list" ref="smsList" @scroll="handleScroll('sms')">
                                <div v-for="item in smsNumbers" :key="item.bh" class="list-item">
                                    <input type="radio" class="custom-radio" :value="item.bh" :checked="selectedSmsId === item.bh"
                                        @click="handleSelection('sms', item.bh)" name="sms-number" />
                                    <span>{{ item.text }}</span>
                                </div>
                            </div>
                            <img :src="expandIcon" class="expand-icon" @click="expandList('sms')" />
                        </template>
                        <div v-else class="placeholder">可添加短信送达信息</div>
                    </div>
                    <div class="cell action">
                        <button class="add-btn" @click="phoneNumberModalOpen = true">+添加</button>
                    </div>
                </div>
                <!-- Email -->
                <div class="delivery-row">
                    <div class="cell type">
                        <div class="title-line">
                            <span>邮箱送达</span>
                        </div>
                        <span class="item-count">(共{{ emails.length }}条)</span>
                    </div>
                    <div class="cell content">
                        <template v-if="emails.length > 0">
                            <div class="scrollable-list" ref="emailList" @scroll="handleScroll('email')">
                                <div v-for="item in emails" :key="item.bh" class="list-item">
                                    <input type="radio" class="custom-radio" :value="item.bh" :checked="selectedEmailId === item.bh"
                                        @click="handleSelection('email', item.bh)" name="email-address" />
                                    <span>{{ item.text }}</span>
                                </div>
                            </div>
                            <img :src="expandIcon" class="expand-icon" @click="expandList('email')" />
                        </template>
                        <div v-else class="placeholder">可添加邮箱送达信息</div>
                    </div>
                    <div class="cell action">
                        <button class="add-btn" @click="emailAddressModalOpen = true">+添加</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal_footer">
            <div class="modal_btn normal" @click="$emit('update:open', false)">
                关闭
            </div>
            <div class="modal_btn" @click="handleConfirm">
                确定生成
            </div>
        </div>
        <transition name="expand-transition">
            <div v-if="expandedList" class="expanded-view">
                <div class="expanded-container">
                    <div class="expanded-header">
                        <span class="expanded-title">{{ expandedTitle }}</span>
                        <div class="collapse-btn" @click="collapseList">
                            <img :src="closeExpandIcon" class="collapse-icon" />
                        </div>
                    </div>
                    <div class="expanded-content">
                        <div class="scrollable-list expanded" ref="expandedScrollList" @scroll="handleExpandedScroll">
                            <div v-for="item in activeList" :key="item.bh" class="list-item">
                                <input type="radio" class="custom-radio" :value="item.bh" :checked="selectedId === item.bh"
                                    @click="handleSelection(expandedList, item.bh)" :name="expandedList" />
                                <span>{{ item.text }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pageBar expanded">
                    <img :src="scrollState[expandedList] && scrollState[expandedList].isAtTop ? pageUpDisabled : pageUp"
                        @mousedown="startExpandedScrolling(-1)" @mouseup="stopScrolling" @mouseleave="stopScrolling" />
                    <img :src="scrollState[expandedList] && scrollState[expandedList].isAtBottom ? pageDownDisabled : pageDown"
                        @mousedown="startExpandedScrolling(1)" @mouseup="stopScrolling" @mouseleave="stopScrolling" />
                </div>
            </div>
        </transition>
    </a-modal>
    <MailArriveAddressModal v-model:open="mailArriveAddressModalOpen" @updateList="getArriveAddressList" />
    <PhoneNumberModal v-model:open="phoneNumberModalOpen" @updateList="getSmsList" />
    <EmailAddressModal v-model:open="emailAddressModalOpen" @updateList="getEmailList" />
    <SignModal v-model:open="signModalOpen" @cancel="signModalOpen = false" @confirm="confirmSign" />
    <a-modal v-model:open="tishiOpen" title="提示" :footer="null" :closable="false" width="1478"
            :centered="true" wrapClassName="ipDialog">
            <div class="modal_content jurisdiction-modal-content">
                <p>您在本平台提供或确认的送达地址，将适用于一审、二审、再审审查、执行程序，以及同期在受理法院审理的其他案件。</p>
                <p>因受送达人自己提供或者确认的电子和线下送达地址不准确、拒不提供送达地址、送达地址变更未及时告知人民法院、受送达人本人或者受送达人指定的代收人拒绝签收，导致诉讼文书未能被受送达人实际接收的，电子送达以文书到达对应系统之日视为送达之日，线下送达以文书退回之日视为送达之日。</p>
            </div>
            <div class="modal_footer">
                <div class="modal_btn" @click="tishiOpen = false">
                    关闭
                </div>
            </div>
        </a-modal>
</template>

<script>
import pageUp from '@/assets/pageUp.png'
import pageDown from '@/assets/pageDown.png'
import pageUpDisabled from '@/assets/pageUp_disable.png'
import pageDownDisabled from '@/assets/pageDown_disable.png'
import expandIcon from '@/assets/icon-expand.png'
import closeExpandIcon from '@/assets/icon-close-expand.png'
import MailArriveAddressModal from '@/components/MailArriveAddressModal.vue'
import PhoneNumberModal from '@/components/PhoneNumberModal.vue'
import EmailAddressModal from '@/components/EmailAddressModal.vue'
import SignModal from '@/components/SignModal.vue'
import * as pdfjsLib from 'pdfjs-dist'

export default {
    components: {
        MailArriveAddressModal,
        PhoneNumberModal,
        EmailAddressModal,
        SignModal,
    },
    props: {
        open: {
            type: Boolean,
            required: true,
        },
    },
    emits: ['update:open', 'updateFileImage'],
    data() {
        return {
            mailArriveAddressModalOpen: false,
            phoneNumberModalOpen: false,
            emailAddressModalOpen: false,
            signModalOpen: false,
            tishiOpen: false,

            formData: {
                id: '', // 将在initFormData方法中初始化
                layyid: '', // 将在initFormData方法中初始化
                fyId: '', // 将在initFormData方法中初始化
                clmc: '送达地址确认书',
                cllx: '', // 将在initFormData方法中初始化
                ssryId: '', // 将在initFormData方法中初始化
                wjs: [],
                ssclid: '', // 将在initFormData方法中初始化
                ywlx: 'layy',
                path: 'layy',
                deleteUrl: '/yzw-zxfw-lafw/api/v3/layy/ssclfj/delete',
                subTitle: '必传材料',
                newFileList: [],
                qmPath: '',
                sddzxxId: '', // 邮寄送达列表的ID
                sjhm: '',
                email: '',
            },

            pageUp,
            pageDown,
            pageUpDisabled,
            pageDownDisabled,
            expandIcon,
            closeExpandIcon,
            scrollInterval: null,
            selectedMailId: null,
            selectedSmsId: null,
            selectedEmailId: null,
            mailAddresses: [],
            smsNumbers: [],
            emails: [],
            scrollState: {
                mail: { isAtTop: true, isAtBottom: false },
                sms: { isAtTop: true, isAtBottom: false },
                email: { isAtTop: true, isAtBottom: false },
            },
            expandedList: null,
        }
    },
    created() {
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(`../utils/pdf.worker.min.js`, import.meta.url).toString()
    },
    watch: {
        open(newVal) {
            if (newVal) {
                this.initBaseData()
                this.initFormData()
            } else {
                this.collapseList() // Close expanded view when modal closes
                this.selectedMailId = null
                this.selectedSmsId = null
                this.selectedEmailId = null
            }
        },
    },
    computed: {
        cclx() {
            let ssclTypeList = this.$store.getters.ajlxInfo?.sscl || []
            return ssclTypeList.find((item) => item.name == '送达地址确认书')?.cllx || ''
        },
        expandedTitle() {
            if (this.expandedList === 'mail') return '邮寄送达地址'
            if (this.expandedList === 'sms') return '短信送达'
            if (this.expandedList === 'email') return '邮箱送达'
            return ''
        },
        activeList() {
            if (this.expandedList === 'mail') return this.mailAddresses
            if (this.expandedList === 'sms') return this.smsNumbers
            if (this.expandedList === 'email') return this.emails
            return []
        },
        selectedId: {
            get() {
                if (this.expandedList === 'mail') return this.selectedMailId
                if (this.expandedList === 'sms') return this.selectedSmsId
                if (this.expandedList === 'email') return this.selectedEmailId
                return null
            },
            set(value) {
                if (this.expandedList === 'mail') this.selectedMailId = value
                if (this.expandedList === 'sms') this.selectedSmsId = value
                if (this.expandedList === 'email') this.selectedEmailId = value
            },
        },
    },
    methods: {
        async getLayyDetail() {
            const layyId = this.$store.getters.layyId || '16aaf4ddb3904d0383ff90396fd0c051'
            return await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${layyId}/0`,
                { method: 'GET' }
            )
        },
        initBaseData() {
            Promise.all([
                this.getArriveAddressList(),
                this.getSmsList(),
                this.getEmailList(),
            ]).finally(() => {
                this.$nextTick(() => {
                    this.updateScrollStates()
                })
            })
        },
        // 获取邮寄送达地址列表
        getArriveAddressList() {
            return this.$rpa
                .dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/yjsd/list',
                    {
                        method: 'POST',
                        body: { pageNum: 1, pageSize: 1000 },
                    },
                    false
                )
                .then((res) => {
                    if (res.data && res.data.data) {
                        res.data.data.forEach((item) => {
                            item.text =
                                item.dzMc +
                                item.xxdz +
                                '(' +
                                item.name +
                                ' ' +
                                item.sjhm +
                                ')'
                        })
                        this.mailAddresses = res.data.data
                    } else {
                        this.mailAddresses = []
                    }
                })
        },

        // 获取短信送达列表
        getSmsList() {
            return this.$rpa
                .dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/dxsd/list',
                    { method: 'POST', body: { pageNum: 1, pageSize: 1000 } },
                    false
                )
                .then((res) => {
                    if (res.data && res.data.data) {
                        res.data.data.forEach((item) => {
                            item.text = item.sjhm
                            item.value = item.bh
                        })
                        this.smsNumbers = res.data.data
                    } else {
                        this.smsNumbers = []
                    }
                })
        },

        // 获取邮箱送达列表
        getEmailList() {
            return this.$rpa
                .dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-yhfw/api/v1/grxx/yxsd/list',
                    { method: 'POST', body: { pageNum: 1, pageSize: 1000 } },
                    false
                )
                .then((res) => {
                    if (res.data && res.data.data) {
                        res.data.data.forEach((item) => {
                            item.text = item.email
                            item.value = item.bh
                        })
                        this.emails = res.data.data
                    } else {
                        this.emails = []
                    }
                })
        },

        handleScroll(type) {
            const el = this.$refs[`${type}List`]
            if (!el) return

            this.scrollState[type].isAtTop = el.scrollTop < 1
            this.scrollState[type].isAtBottom =
                el.scrollTop + el.clientHeight >= el.scrollHeight - 1
        },
        startScrolling(type, direction) {
            this.stopScrolling()
            const el = this.$refs[`${type}List`]
            if (!el) return

            this.scrollInterval = setInterval(() => {
                el.scrollBy({ top: 30 * direction, behavior: 'smooth' })
            }, 50)
        },
        stopScrolling() {
            clearInterval(this.scrollInterval)
        },
        updateScrollStates() {
            this.handleScroll('mail')
            this.handleScroll('sms')
            this.handleScroll('email')
        },
        expandList(type) {
            this.expandedList = type
            this.$nextTick(() => {
                this.handleExpandedScroll()
            })
        },
        collapseList() {
            const listKey = this.expandedList
            if (!listKey) {
                this.expandedList = null
                return
            }

            let list, selectedId

            if (listKey === 'mail') {
                list = this.mailAddresses
                selectedId = this.selectedMailId
            } else if (listKey === 'sms') {
                list = this.smsNumbers
                selectedId = this.selectedSmsId
            } else if (listKey === 'email') {
                list = this.emails
                selectedId = this.selectedEmailId
            }

            if (list && selectedId !== null) {
                const selectedIndex = list.findIndex(
                    (item) => item.bh === selectedId
                )
                if (selectedIndex > 0) {
                    const [selectedItem] = list.splice(selectedIndex, 1)
                    list.unshift(selectedItem)
                }
            }

            this.expandedList = null
        },
        handleExpandedScroll() {
            const type = this.expandedList
            if (!type) return
            const el = this.$refs.expandedScrollList
            if (!el) return

            this.scrollState[type].isAtTop = el.scrollTop < 1
            this.scrollState[type].isAtBottom =
                el.scrollTop + el.clientHeight >= el.scrollHeight - 1
        },
        startExpandedScrolling(direction) {
            this.stopScrolling()
            const el = this.$refs.expandedScrollList
            if (!el) return

            this.scrollInterval = setInterval(() => {
                el.scrollBy({ top: 30 * direction, behavior: 'smooth' })
            }, 50)
        },
        handleConfirm() {
            if (!this.selectedMailId) {
                this.$message.warn('请选择邮寄送达地址')
                return
            }
            this.signModalOpen = true
        },
        async confirmSign(sign) {
            this.formData.sddzxxId = this.selectedMailId
            if(this.selectedSmsId) {
                this.formData.sjhm = this.$getText(this.smsNumbers, this.selectedSmsId)
            }
            if(this.selectedEmailId) {
                this.formData.email = this.$getText(this.emails, this.selectedEmailId)
            }
            this.formData.qmPath = sign.osspath
            let res = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/ssclfj/upload`,
                {
                    method: 'POST',
                    body: this.formData,
                }
            )
            if(res.code == 200 && res.data) {
                let layyInfoRes = await this.getLayyDetail()
                if(layyInfoRes.data && layyInfoRes.data.sscls) {
                    let sdclList = layyInfoRes.data.sscls.find(item => item.cllx == '11800016-254')
                    if(sdclList) {
                        let pdfFileList = sdclList.wjs
                        let pdfFile = pdfFileList.find(item => item.id == res.data.id)
                        if(pdfFile) {
                            // debugger
                            // let pdfPath = pdfFile.url
                            // // 将此pdfPath网络地址的pdf转化成图片
                            // const imageBase64 =
                            //     await this.convertPdfUrlToBase64Image(pdfPath)
                            this.$emit('updateFileImage', pdfFile)
                            this.$emit('update:open', false)
                            this.signModalOpen = false
                        }
                    }
                }
            }
        },
        async convertPdfUrlToBase64Image(url) {
            const loadingTask = pdfjsLib.getDocument(url)
            const pdf = await loadingTask.promise
            const page = await pdf.getPage(1) 
            const viewport = page.getViewport({ scale: 2.0 })

            const canvas = document.createElement('canvas')
            const context = canvas.getContext('2d')
            canvas.height = viewport.height
            canvas.width = viewport.width

            const renderContext = {
                canvasContext: context,
                viewport: viewport,
            }

            await page.render(renderContext).promise

            return canvas.toDataURL('image/jpeg')
        },
        // 初始化表单数据
        initFormData() {
            // 确保store中有必要的数据
            const layyInfo = this.$store.getters.layyInfo
            const layyId = this.$store.getters.layyId
            const fyId = this.$store.getters.fyId
            
            // 如果缺少必要数据，先获取
            if (!layyInfo || !layyId) {
                this.getLayyDetail().then(res => {
                    if (res.code === 200 && res.data) {
                        this.$store.dispatch('layy/setLayyInfo', res.data)
                        this.setFormDataFromStore()
                    }
                })
            } else {
                this.setFormDataFromStore()
            }
        },
        
        // 从store中设置表单数据
        setFormDataFromStore() {
            const layyInfo = this.$store.getters.layyInfo
            const cllx = this.cclx
            
            if (layyInfo && layyInfo.sscls && cllx) {
                const ssclItem = layyInfo.sscls.find(item => item.cllx === cllx)
                
                if (ssclItem) {
                    this.formData.id = ssclItem.id
                    this.formData.ssclid = ssclItem.id
                    this.formData.ssryId = ssclItem.ssryId
                    this.formData.cllx = cllx
                    this.formData.layyid = this.$store.getters.layyId
                    this.formData.fyId = this.$store.getters.fyId
                }
            }
        },
        handleSelection(type, bh) {
            if (type === 'mail') {
                this.selectedMailId = bh
            } else if (type === 'sms') {
                this.selectedSmsId = this.selectedSmsId === bh ? null : bh
            } else if (type === 'email') {
                this.selectedEmailId = this.selectedEmailId === bh ? null : bh
            }
        },
    },
}
</script>

<style>
.arrive-documnet-dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.arrive-documnet-dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.arrive-documnet-dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 24px;
    color: #333333;
    padding: 30px;
    border-bottom: 2px solid #dbe6f1;
    position: relative;
}

.arrive-documnet-dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.arrive-documnet-dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    cursor: pointer;
    border: none;
}
.arrive-documnet-dialog .modal_btn.normal {
    background: #fff;
    color: #3173c6;
    border: 1px solid #3173c6;
}
</style>

<style scoped>
.delivery-table {
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.delivery-row {
    display: flex;
    min-height: 80px;
    border-bottom: 1px solid #e0e0e0;
}

.delivery-row:last-child {
    border-bottom: none;
}

.delivery-row .cell {
    padding: 10px 15px;
    display: flex;
    align-items: center;
}

.delivery-row .cell.type {
    width: 200px;
    background-color: #f7f9fc;
    border-right: 1px solid #e0e0e0;
    justify-content: center;
    font-weight: 500;
    flex-direction: column;
}

.delivery-row .cell.type.required .title-line::before {
    content: '*';
    color: red;
    margin-right: 4px;
}

.delivery-row .cell.content {
    flex: 1;
    padding-left: 30px;
    display: flex;
    align-items: center;
    max-width: 660px;
}

.delivery-row .cell.action {
    width: 150px;
    border-left: 1px solid #e0e0e0;
    justify-content: center;
}
.expand-icon {
    cursor: pointer;
    width: 32px;
    height: 32px;
    margin-left: 20px;
    flex-shrink: 0;
}

.delivery-row.header .cell {
    font-weight: bold;
}

.add-btn {
    background-color: #3173c6;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 20px;
    font-size: 20px;
    cursor: pointer;
    align-items: center;
    width: 100px;
    height: 42px;
    justify-content: space-between;
}

.list-container {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
}

.scrollable-list {
    height: 155px;
    overflow-y: auto;
    flex-grow: 1;
}
.scrollable-list::-webkit-scrollbar {
    display: none;
}

.list-item {
    padding: 5px 0;
    display: flex;
    align-items: center;
    border-bottom: none;
}

.list-item span {
    margin-left: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pageBar {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20px;
    height: 120px;
}
.pageBar img {
    cursor: pointer;
    width: 40px;
    height: 40px;
}
.placeholder {
    color: #999;
    height: 155px;
    line-height: 155px;
}

.custom-radio {
    appearance: none;
    -webkit-appearance: none;
    width: 40px;
    height: 40px;
    border: 1px solid #ccc;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    flex-shrink: 0;
}

.custom-radio:checked {
    background-color: #3173c6;
    border-color: #3173c6;
}

.custom-radio:checked::after {
    content: '';
    position: absolute;
    left: 14px;
    top: 6px;
    width: 10px;
    height: 20px;
    border: solid white;
    border-width: 0 4px 4px 0;
    transform: rotate(45deg);
}

.expanded-view {
    position: absolute;
    top: 100px;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 10;
    display: flex;
    align-items: center;
    padding: 20px 50px;
    padding-bottom: 30px;
    padding-right: 15px;
}
.expanded-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    border: 1px solid #a1b1c5;
    flex-grow: 1;
}
.expanded-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
    position: relative;
    background-color: #f1f5ff;
    flex-shrink: 0;
}
.expanded-title {
    font-size: 28px;
    font-weight: bold;
    color: #333333;
}
.collapse-btn {
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}
.collapse-icon {
    width: 40px;
    height: 40px;
    transform: rotate(180deg);
}
.expanded-content {
    flex-grow: 1;
    display: flex;
    overflow: hidden;
    padding: 20px;
    background-color: #fff;
}
.expanded-content .scrollable-list.expanded {
    height: 100%;
    flex-grow: 1;
}
.expanded-content .scrollable-list .list-item {
    font-size: 24px;
    padding: 10px 0;
}
.pageBar.expanded {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 20px;
    flex-shrink: 0;
}
.pageBar.expanded img:first-child {
    margin-bottom: 300px;
}

.expand-transition-enter-active,
.expand-transition-leave-active {
    transition: all 0.3s ease;
}

.expand-transition-enter-from,
.expand-transition-leave-to {
    opacity: 0;
    transform: scale(0.95);
}

.item-count {
    color: #78808a;
    font-size: 24px;
    font-weight: 400;
}
</style>