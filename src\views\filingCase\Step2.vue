<template>
    <div>
        <Container>
            <template #head>
                <div class="ip_head">民事一审
                    <Steps :active="1" />
                </div>

            </template>
            <div class="con">
                {{ $store.getters.lafsName }}-{{ $store.getters.applyCaseTypeName}}
                <div class="border">
                    <div class="warning"><img src="@/assets/danger.png"
                            alt="" />请您判断您的纠纷符合以下哪个案由类型，并对应选择，该选项不会影响您的立案申请的审核结果</div>
                    <div class="case_reason_row">
                        <div class="choose_case_reason" @click="openReasonDialog">
                            {{caseReason && caseReason.text||'请选择立案案由'}}
                        </div>
                        <div class="change_reason" @click="openReasonDialog" v-if="caseReason && caseReason.text">修改
                        </div>
                    </div>
                    <div class="reason_title">常用案由</div>
                    <div class="common_reason_list">
                        <div class="common_reason_item" :class="{active:caseReason && (item.text == caseReason.text)}"
                            v-for="(item,index) in commonCaseReasonList" :key="index"
                            @click="chooseCommonCaseReason(item)">{{item.text}}</div>
                        <div class="common_reason_item empty_item" v-for="item in 4" :key="item"></div>
                    </div>
                </div>
                <div class="btns">
                    <div class="ip_btn deep_btn" @click="goNext">下一步</div>
                </div>
            </div>
        </Container>
        <a-modal v-model:open="reasonDialog" title="选择立案案由" :footer="null" :closable="false" width="873px"
            :centered="true" wrapClassName="Step2_Dialog">
            <div class="modal_content">
                <div class="modal_search">
                    <input placeholder="搜索关键词" v-model="searchText">
                    <div @click="searchReason">搜索</div>
                </div>
                <div class="reason_list_con">
                    <div class="reason_list" ref="reasonList" @scroll="checkScrollStatus">
                        <template v-if="tempReasonList.length > 0">
                            <div class="reason_item" :ref="`reason_item_${item.id}`"
                                :class="{active: tempCaseReason && (item.id === tempCaseReason.value)}"
                                v-for="(item,index) in tempReasonList" :key="index"
                                @click="chooseCaseReason({text: item.name,value:item.id})">
                                {{item.name}}
                            </div>
                        </template>
                        <div v-else class="no-data-container">
                            <img src="@/assets/nodata.png" alt="暂无数据" class="no-data-image" />
                            <div class="no-data-text">暂无数据</div>
                        </div>
                    </div>
                    <div class="pageBar">
                        <img :src="pageUpImage"
                             @click="canScrollUp && scrollPage(-1)"
                             :class="{ disabled: !canScrollUp }" />
                        <img :src="pageDownImage"
                             @click="canScrollDown && scrollPage(1)"
                             :class="{ disabled: !canScrollDown }" />
                    </div>
                </div>

            </div>
            <div class="modal_footer">
                <div class="modal_btn normal_btn" @click="reasonDialog = false">
                    关闭
                </div>
                <div class="modal_btn" @click="confirmReason">
                    确定
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script>
import Container from '@/components/Container.vue'
import Steps from '@/components/Steps.vue'
export default {
    components: {
        Container,
        Steps,
    },
    data() {
        return {
            caseReason: null,
            tempCaseReason: null,
            canScrollUp: false, // 是否可以向上滚动
            canScrollDown: false, // 是否可以向下滚动
            commonCaseReasonList: [
                {
                    text: '民间借贷纠纷',
                    value: '9181',
                },
                {
                    text: '离婚纠纷',
                    value: '9015',
                },
                {
                    text: '金融借款合同纠纷',
                    value: '9178',
                },
                {
                    text: '物业服务合同纠纷',
                    value: '9280',
                },
                {
                    text: '机动车交通事故责任纠纷',
                    value: '9722',
                },
                {
                    text: '融资租赁合同纠纷',
                    value: '9199',
                },
                {
                    text: '银行信用卡纠纷',
                    value: '9193',
                },
                {
                    text: '劳动争议',
                    value: '9462',
                },
                {
                    text: '买卖合同纠纷',
                    value: '9142',
                },
                {
                    text: '保证保险合同纠纷',
                    value: '9675',
                },
                {
                    text: '证券虚假陈述责任纠纷',
                    value: '9648',
                },
            ],
            caseReasonList: [],
            tempReasonList: [],
            searchText: '',
            reasonDialog: false,
        }
    },
    computed: {
        // 上滚按钮图片
        pageUpImage() {
            return this.canScrollUp ? require('@/assets/pageUp.png') : require('@/assets/pageUp_disable.png')
        },
        // 下滚按钮图片
        pageDownImage() {
            return this.canScrollDown ? require('@/assets/pageDown.png') : require('@/assets/pageDown_disable.png')
        }
    },
    async created() {
        let layyInfoRes = await this.$rpa.dealFetch(
            `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
            { method: 'GET' }
        )
        
        if (layyInfoRes.code === 200 && layyInfoRes.data) {
            // 将立案预约信息存入store
            this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)
            this.$store.dispatch('layy/setLafs', layyInfoRes.data.layy.lafs)
        }
    },
    mounted() {
        this.getReasonList()
    },
    methods: {
        async getReasonList() {
            let reasonList = []
            let reasonListStr = localStorage.getItem('caseReasonList')
            if (!reasonListStr) {
                let res = await this.$rpa.dealFetch(
                    'https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v1/ay/tree/batch?lbs=0300',
                    { method: 'GET' }
                )
                let list = res.data.data['0300'][0].children
                function flattenChildren(list) {
                    return list.reduce((acc, node) => {
                        acc.push(node) // 添加当前节点
                        if (node.children?.length) {
                            acc.push(...flattenChildren(node.children)) // 递归处理子节点并合并
                        }
                        return acc
                    }, [])
                }
                function deduplicateByName(list) {
                    const uniqueMap = new Map()
                    list.forEach((item) => {
                        if (!uniqueMap.has(item.name)) {
                            uniqueMap.set(item.name, item)
                        }
                    })
                    return Array.from(uniqueMap.values())
                }
                let flatList = flattenChildren(list)
                reasonList = deduplicateByName(flatList)
                localStorage.setItem(
                    'caseReasonList',
                    JSON.stringify(reasonList)
                )
            } else {
                reasonList = JSON.parse(reasonListStr)
            }
            this.caseReasonList = reasonList
            this.tempReasonList = this.caseReasonList
            if(this.$store.getters.layyInfo.layy && this.$store.getters.layyInfo.layy.laayMz) {
                this.caseReason = this.caseReasonList.find(item => item.id == this.$store.getters.layyInfo.layy.laayMz)
                if(!this.caseReason) {
                    return
                }
                this.caseReason.text = this.caseReason.name
                this.caseReason.value = this.caseReason.id
            }
        },
        searchReason() {
            if (this.searchText) {
                this.tempReasonList = this.caseReasonList.filter((item) => {
                    return item.name.includes(this.searchText)
                })
            } else {
                this.tempReasonList = this.caseReasonList
            }
            // 搜索后检查滚动状态
            this.$nextTick(() => {
                this.checkScrollStatus()
            })
        },
        closeReasonDialog() {
            this.reasonDialog = false
        },
        async openReasonDialog() {
            this.searchText = null
            this.searchReason()
            this.reasonDialog = true
            this.tempCaseReason = this.caseReason

            // 等待DOM更新后检查滚动状态
            await this.$nextTick()
            this.checkScrollStatus()

            if (this.tempCaseReason) {
                this.chooseCaseReason({text: this.caseReason.text,value:this.caseReason.value})
                const element = this.$refs[`reason_item_${this.caseReason.value}`]
                if (element && element[0]) {
                    element[0].scrollIntoView({
                        block: 'center',
                        behavior: 'smooth',
                    })
                    // 滚动后再次检查状态
                    setTimeout(() => {
                        this.checkScrollStatus()
                    }, 300)
                }
            }
        },
        chooseCommonCaseReason(item) {
            this.caseReason = item
        },
        chooseCaseReason(item) {
            this.tempCaseReason = item
        },
        confirmReason() {
            this.caseReason = this.tempCaseReason
            this.reasonDialog = false
        },

        scrollPage(count) {
            let div = this.$refs.reasonList
            const pageHeight = div.clientHeight
            div.scrollBy({
                top: pageHeight * count,
                behavior: 'smooth',
            })
            // 滚动后检查滚动状态
            setTimeout(() => {
                this.checkScrollStatus()
            }, 300) // 等待滚动动画完成
        },
        // 检查滚动状态
        checkScrollStatus() {
            const div = this.$refs.reasonList
            if (!div) return

            // 检查是否可以向上滚动
            this.canScrollUp = div.scrollTop > 0

            // 检查是否可以向下滚动
            this.canScrollDown = div.scrollTop < (div.scrollHeight - div.clientHeight)
        },
        async getLayyDetail() {
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy/layyxq/${this.$store.getters.layyId}/0`,
                { method: 'GET' }
            )

            if (layyInfoRes.code === 200 && layyInfoRes.data) {
                // 将立案预约信息存入store
                this.$store.dispatch('layy/setLayyInfo', layyInfoRes.data)

                return true
            }
        },
        // 更新立案案由
        async updateLayy() {
            let param = {
                id: this.$store.getters.layyId,
                laayMz: this.caseReason ? this.caseReason.value : '',
                laay: this.caseReason ? this.caseReason.text : '',
                gxhYs: this.caseReason ? 2 : '', // TODO此处暂不确定，先写死2
            }
            let layyInfoRes = await this.$rpa.dealFetch(
                `https://zxfw.court.gov.cn/yzw/yzw-zxfw-lafw/api/v3/layy`,
                {
                    method: 'PATCH',
                    body: param,
                }
            )
            if (layyInfoRes.code === 200) {
                this.getLayyDetail()
            } else {
                this.$messageConfirm({
                    message: '立案预约失败',
                    confirmText: '确定',
                })
                return false
            }
        },
        async goNext() {
            await this.updateLayy()
            if (this.$store.getters.lafs == '2') {
                this.$router.push('/step3-0')
            } else {
                this.$router.push('/step3')
            }
        },
    },
}
</script>

<style scoped>
.warning {
    height: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 20px;
    color: #c67b31;
    align-self: flex-start;
    display: flex;
    align-items: center;
}
.warning img {
    margin-right: 5px;
}
.case_reason_row {
    height: 72px;
    margin-top: 10px;
    align-self: flex-start;
    display: flex;
}
.choose_case_reason {
    width: 540px;
    height: 72px;
    line-height: 72px;
    text-align: center;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 0 20px;
}

.change_reason {
    height: 72px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 18px;
}

.reason_title {
    height: 42px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 42px;
    color: #333333;
    align-self: flex-start;
    margin-top: 64px;
}
.common_reason_list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.common_reason_item {
    width: 398px;
    height: 72px;
    background: #f1f5ff;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
}

.common_reason_item.active {
    background: #3173c6 !important;
    color: #fff !important;
}

.empty_item {
    height: 0;
    margin-top: 0;
}

.modal_search {
    display: flex;
}

.modal_search input {
    width: 730px;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    outline: none;
    padding-left: 20px;
}

.modal_search input::placeholder {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 32px;
    color: #a1b1c5;
}

.modal_search div {
    width: 104px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -20px;
}

.reason_list_con {
    margin-top: 20px;
    height: 546px;
    padding: 5px;
    background: #f1f5ff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    width: 706px;
    position: relative;
}

.reason_list {
    height: 536px;
    overflow: auto;
    border-radius: 10px 10px 10px 10px;
    background: #fff;
    border: 1px solid #a1b1c5;
}

.reason_item {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 32px;
    color: #78808a;
    min-height: 72px;
    border-bottom: 1px solid #a1b1c5;
    padding-left: 20px;
    display: flex;
    align-items: center;
}

.reason_item.active {
    background: #3173c6;
    color: #ffffff;
}

.no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
}

.no-data-image {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
}

.no-data-text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 32px;
    color: #a1b1c5;
}

.pageBar {
    height: 546px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    position: absolute;
    right: -65px;
    top: 0;
}
.pageBar img {
    margin: 45px 0;
    cursor: pointer;
}

.pageBar img.disabled {
    cursor: not-allowed;
    opacity: 0.6;
}
</style>


<style>
.Step2_Dialog .ant-modal-content {
    padding: 0;
    border-radius: 20px;
    overflow: hidden;
}

.Step2_Dialog .ant-modal-title {
    height: 100px;
    background: #dbe6f1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 42px;
    color: #03206a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.Step2_Dialog .modal_content {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 30px;
    color: #333333;
    padding: 0 50px 20px 50px;
    border-bottom: 2px solid #dbe6f1;
}

.Step2_Dialog .modal_footer {
    height: 112px;
    padding: 0 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.Step2_Dialog .modal_btn {
    width: 216px;
    height: 72px;
    background: #3173c6;
    border-radius: 10px 10px 10px 10px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    border: 1px solid #3173c6;
}
.normal_btn {
    background: #fff !important;
    color: #3173c6 !important;
}
</style>

<style scoped>
.con {
    display: flex;
    align-items: center;
    height: 100%;
    flex-direction: column;
    padding: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 30px;
    color: #333333;
    font-weight: bold;
}

.border {
    width: 100%;
    height: 564px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #a1b1c5;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 10px 30px;
}

.ip_head {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: bold;
    font-size: 32px;
    color: #333333;
}

.btns {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
}

.ip_btn {
    width: 216px;
    height: 72px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #3173c6;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 32px;
    color: #3173c6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 12px;
}

.deep_btn {
    background: #3173c6;
    color: #ffffff;
}
</style>